package database

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"math"
	"strings"
	"sync"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// SQLiteWrapper 提供带重试机制的SQLite数据库包装器
type SQLiteWrapper struct {
	db     *sql.DB
	logger *slog.Logger
	config *SQLiteConfig
	mutex  sync.RWMutex
}

// SQLiteConfig SQLite配置
type SQLiteConfig struct {
	MaxRetries      int           // 最大重试次数
	BaseDelay       time.Duration // 基础延迟
	MaxDelay        time.Duration // 最大延迟
	BackoffFactor   float64       // 退避因子
	BusyTimeout     time.Duration // SQLite busy timeout
	MaxOpenConns    int           // 最大打开连接数
	MaxIdleConns    int           // 最大空闲连接数
	ConnMaxLifetime time.Duration // 连接最大生存时间
}

// DefaultSQLiteConfig 返回默认配置
func DefaultSQLiteConfig() *SQLiteConfig {
	return &SQLiteConfig{
		MaxRetries:      5,
		BaseDelay:       50 * time.Millisecond,
		MaxDelay:        5 * time.Second,
		BackoffFactor:   2.0,
		BusyTimeout:     30 * time.Second,
		MaxOpenConns:    1, // SQLite建议单连接写入
		MaxIdleConns:    1,
		ConnMaxLifetime: 0, // 永不过期
	}
}

// NewSQLiteWrapper 创建新的SQLite包装器
func NewSQLiteWrapper(dbPath string, config *SQLiteConfig) (*SQLiteWrapper, error) {
	if config == nil {
		config = DefaultSQLiteConfig()
	}

	// 构建优化的DSN
	dsn := buildOptimizedDSN(dbPath, config)

	db, err := sql.Open("sqlite3", dsn)
	if err != nil {
		return nil, fmt.Errorf("打开数据库失败: %w", err)
	}

	// 配置连接池
	db.SetMaxOpenConns(config.MaxOpenConns)
	db.SetMaxIdleConns(config.MaxIdleConns)
	db.SetConnMaxLifetime(config.ConnMaxLifetime)

	wrapper := &SQLiteWrapper{
		db:     db,
		logger: slog.Default(),
		config: config,
	}

	// 测试连接并设置优化参数
	if err := wrapper.initialize(); err != nil {
		db.Close()
		return nil, fmt.Errorf("初始化数据库失败: %w", err)
	}

	return wrapper, nil
}

// buildOptimizedDSN 构建优化的DSN字符串
func buildOptimizedDSN(dbPath string, config *SQLiteConfig) string {
	params := []string{
		"_journal_mode=WAL",
		"_synchronous=NORMAL",
		"_cache_size=20000",
		"_temp_store=memory",
		fmt.Sprintf("_busy_timeout=%d", int(config.BusyTimeout.Milliseconds())),
		"_wal_autocheckpoint=1000",
		"_foreign_keys=ON",
		"cache=shared",
	}

	return fmt.Sprintf("%s?%s", dbPath, strings.Join(params, "&"))
}

// initialize 初始化数据库设置
func (w *SQLiteWrapper) initialize() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 设置额外的SQLite优化参数
	pragmas := []string{
		"PRAGMA mmap_size = 134217728", // 128MB
		"PRAGMA optimize",
	}

	for _, pragma := range pragmas {
		if err := w.ExecWithRetry(ctx, pragma); err != nil {
			w.logger.Warn("设置SQLite参数失败", "pragma", pragma, "error", err)
		}
	}

	return nil
}

// ExecWithRetry 带重试机制的Exec
func (w *SQLiteWrapper) ExecWithRetry(ctx context.Context, query string, args ...interface{}) error {
	return w.retryOperation(ctx, func() error {
		_, err := w.db.ExecContext(ctx, query, args...)
		return err
	})
}

// QueryRowWithRetry 带重试机制的QueryRow
func (w *SQLiteWrapper) QueryRowWithRetry(ctx context.Context, query string, args ...interface{}) *sql.Row {
	// QueryRow不会立即执行，所以这里直接返回
	return w.db.QueryRowContext(ctx, query, args...)
}

// QueryWithRetry 带重试机制的Query
func (w *SQLiteWrapper) QueryWithRetry(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	var rows *sql.Rows
	err := w.retryOperation(ctx, func() error {
		var err error
		rows, err = w.db.QueryContext(ctx, query, args...)
		return err
	})
	return rows, err
}

// BeginTxWithRetry 带重试机制的事务开始
func (w *SQLiteWrapper) BeginTxWithRetry(ctx context.Context, opts *sql.TxOptions) (*sql.Tx, error) {
	var tx *sql.Tx
	err := w.retryOperation(ctx, func() error {
		var err error
		tx, err = w.db.BeginTx(ctx, opts)
		return err
	})
	return tx, err
}

// ExecuteInTransaction 在事务中执行操作
func (w *SQLiteWrapper) ExecuteInTransaction(ctx context.Context, fn func(*sql.Tx) error) error {
	return w.retryOperation(ctx, func() error {
		tx, err := w.db.BeginTx(ctx, nil)
		if err != nil {
			return err
		}
		defer tx.Rollback()

		if err := fn(tx); err != nil {
			return err
		}

		return tx.Commit()
	})
}

// retryOperation 重试操作的核心逻辑
func (w *SQLiteWrapper) retryOperation(ctx context.Context, operation func() error) error {
	var lastErr error
	
	for attempt := 0; attempt <= w.config.MaxRetries; attempt++ {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		lastErr = operation()
		if lastErr == nil {
			return nil
		}

		// 检查是否是可重试的错误
		if !w.isRetryableError(lastErr) {
			return lastErr
		}

		// 最后一次尝试，不再等待
		if attempt == w.config.MaxRetries {
			break
		}

		// 计算退避延迟
		delay := w.calculateBackoffDelay(attempt)
		w.logger.Debug("数据库操作重试",
			"attempt", attempt+1,
			"max_retries", w.config.MaxRetries,
			"delay", delay,
			"error", lastErr)

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
		}
	}

	return fmt.Errorf("数据库操作失败，已重试%d次: %w", w.config.MaxRetries, lastErr)
}

// isRetryableError 判断错误是否可重试
func (w *SQLiteWrapper) isRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())
	retryableErrors := []string{
		"database is locked",
		"sqlite_busy",
		"database table is locked",
		"cannot start a transaction within a transaction",
	}

	for _, retryableErr := range retryableErrors {
		if strings.Contains(errStr, retryableErr) {
			return true
		}
	}

	return false
}

// calculateBackoffDelay 计算退避延迟
func (w *SQLiteWrapper) calculateBackoffDelay(attempt int) time.Duration {
	delay := float64(w.config.BaseDelay) * math.Pow(w.config.BackoffFactor, float64(attempt))
	if delay > float64(w.config.MaxDelay) {
		delay = float64(w.config.MaxDelay)
	}
	return time.Duration(delay)
}

// GetDB 获取原始数据库连接（谨慎使用）
func (w *SQLiteWrapper) GetDB() *sql.DB {
	return w.db
}

// Close 关闭数据库连接
func (w *SQLiteWrapper) Close() error {
	w.mutex.Lock()
	defer w.mutex.Unlock()
	
	if w.db != nil {
		return w.db.Close()
	}
	return nil
}

// Stats 获取连接池统计信息
func (w *SQLiteWrapper) Stats() sql.DBStats {
	return w.db.Stats()
}
