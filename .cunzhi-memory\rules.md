# 开发规范和规则


**项目结构参考：**
- `go-mail-client/` - 客户端代码
- `go-mail-backend/` - 后台管理系统
- `mail-frontend/` - 后台前端系统
**参考文件：**
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend\internal\api\server.go` - API路由管理
- `@e:\ProjectCode\GoCode\go-mail/go-mail-backend\cmd\single-login/` - 单账号测试流程（包含登录、取邮件列表、取邮件内容、别名操作等完整过程）
---


- 用户明确要求：不要生成总结性Markdown文档，不要生成测试脚本，需要帮助编译，不要运行（用户自己运行）


- SQLite数据库锁定问题：7个定时任务并发访问同一SQLite数据库导致BUSY错误。主要问题：1)无事务管理 2)无重试机制 3)长时间持有数据库锁 4)缺少并发控制。需要添加事务包装、重试机制和优化SQL操作。
